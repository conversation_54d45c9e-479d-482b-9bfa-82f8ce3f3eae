# Qt6 ControlWindow

This is a minimal Qt6 implementation of the ControlWindow that replaces the original Windows API version.

## Features

- **Minimal Qt6 Window**: Simple, clean interface using Qt6 widgets
- **Compatible Interface**: Maintains the same `CW_Register()` and `CW_Create()` function signatures
- **Event Forwarding**: All Qt events (mouse, keyboard, paint) are forwarded to the original WndProc
- **System Menu**: Right-click context menu with all original system commands
- **Cross-Platform Ready**: Uses Qt6 for potential cross-platform compatibility

## Key Components

### ControlWindow Class
- Inherits from `QMainWindow`
- Handles Qt events and converts them to Windows messages
- Forwards events to the registered window procedure
- Provides system menu with application launchers

### Event Handling
- **Mouse Events**: Click, move, wheel events converted to Windows messages
- **Keyboard Events**: Key press/release and character input
- **Paint Events**: Forwarded for custom drawing
- **System Menu**: Context menu for launching applications

### Compatibility Layer
- `CW_Register(WNDPROC)`: Registers window procedure (same as original)
- `CW_Create(uhid, width, height)`: Creates Qt window (same as original)
- Returns `HWND` equivalent (pointer cast for compatibility)

## Building

### Prerequisites
- Qt6 (Core and Widgets modules)
- CMake 3.16 or later
- Visual Studio 2022 (or compatible compiler)

### Build Steps
1. Run `build_qt.bat` or use CMake manually:
   ```bash
   mkdir build_qt
   cd build_qt
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
   ```

### Testing
Run the test application:
```bash
./build_qt/Release/TestControlWindow.exe
```

## Usage

The Qt6 ControlWindow is a drop-in replacement for the original Windows API version:

```cpp
// Register window procedure (same as before)
CW_Register(MyWndProc);

// Create window (same as before)
HWND hWnd = CW_Create(clientId, 800, 600);
```

## System Menu Commands

Right-click the window to access:
- **Fullscreen**: Toggle fullscreen mode
- **Start Explorer**: Launch Windows Explorer
- **Run...**: Open Run dialog
- **Start Powershell**: Launch PowerShell
- **Start Chrome**: Launch Google Chrome
- **Start Brave**: Launch Brave Browser
- **Start Edge**: Launch Microsoft Edge
- **Start Firefox**: Launch Mozilla Firefox
- **Start Internet Explorer**: Launch Internet Explorer

## Technical Details

### Message Translation
Qt events are translated to Windows messages:
- `QMouseEvent` → `WM_LBUTTONDOWN`, `WM_MOUSEMOVE`, etc.
- `QKeyEvent` → `WM_KEYDOWN`, `WM_KEYUP`, `WM_CHAR`
- `QPaintEvent` → `WM_PAINT`
- Menu actions → `WM_SYSCOMMAND`

### Window Properties
- Resizable with min/max buttons
- Minimum size: 400x300 pixels
- Window title shows client IP address
- System menu accessible via right-click

## Integration

This Qt6 version integrates seamlessly with the existing HVNC server code. The `Server.cpp` file continues to work without modifications, as the interface remains identical.
