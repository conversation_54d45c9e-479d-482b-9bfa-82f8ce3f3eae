cmake_minimum_required(VERSION 3.16)
project(HVNCServer)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Gui)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add main executable
add_executable(HVNCServer
    Main.cpp
    Server.cpp
    ControlWindow.cpp
)

# Link Qt6 libraries
target_link_libraries(HVNCServer
    Qt6::Core
    Qt6::Widgets
    Qt6::Gui
    ws2_32
)

# Set target properties
set_target_properties(HVNCServer PROPERTIES
    WIN32_EXECUTABLE TRUE
    OUTPUT_NAME "Server"
)

# Include directories
target_include_directories(HVNCServer PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
