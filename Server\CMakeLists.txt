cmake_minimum_required(VERSION 3.16)
project(HVNCServer)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)

# Add main executable
add_executable(HVNCServer
    Main.cpp
    Server.cpp
    ControlWindow.cpp
)

# Add test executable
add_executable(TestControlWindow
    test_controlwindow.cpp
    ControlWindow.cpp
)

# Link Qt6 libraries to main executable
target_link_libraries(HVNCServer
    Qt6::Core
    Qt6::Widgets
    ws2_32
)

# Link Qt6 libraries to test executable
target_link_libraries(TestControlWindow
    Qt6::Core
    Qt6::Widgets
    ws2_32
)

# Set target properties
set_target_properties(HVNCServer PROPERTIES
    WIN32_EXECUTABLE TRUE
)

set_target_properties(TestControlWindow PROPERTIES
    WIN32_EXECUTABLE TRUE
)

# Include directories
target_include_directories(HVNCServer PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
target_include_directories(TestControlWindow PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
