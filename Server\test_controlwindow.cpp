#include "ControlWindow.h"
#include <QtWidgets/QApplication>
#include <QtCore/QDebug>
#include <winsock.h>

// Mock window procedure for testing
long long CALLBACK TestWndProc(void* hWnd, unsigned int msg, unsigned long long wParam, long long lParam)
{
    qDebug() << "TestWndProc called - msg:" << QString::number(msg, 16) 
             << "wParam:" << wParam << "lParam:" << lParam;
    
    // Handle some basic messages
    switch (msg) {
        case 0x0001: // WM_CREATE
            qDebug() << "Window created!";
            break;
        case 0x000F: // WM_PAINT
            qDebug() << "Paint event";
            break;
        case 0x0002: // WM_DESTROY
            qDebug() << "Window destroyed!";
            QApplication::quit();
            break;
        case 0x0112: // WM_SYSCOMMAND
            qDebug() << "System command:" << wParam;
            break;
        case 0x0200: // WM_MOUSEMOVE
            qDebug() << "Mouse move";
            break;
        case 0x0201: // WM_LBUTTONDOWN
            qDebug() << "Left button down";
            break;
        case 0x0202: // WM_LBUTTONUP
            qDebug() << "Left button up";
            break;
        case 0x0100: // WM_KEYDOWN
            qDebug() << "Key down:" << wParam;
            break;
        case 0x0101: // WM_KEYUP
            qDebug() << "Key up:" << wParam;
            break;
        case 0x0102: // WM_CHAR
            qDebug() << "Char:" << (char)wParam;
            break;
    }
    
    return 0;
}

int main(int argc, char *argv[])
{
    // Initialize Winsock (required for inet_ntoa)
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);
    
    QApplication app(argc, argv);
    
    qDebug() << "Testing Qt6 ControlWindow...";
    
    // Register our test window procedure
    if (!CW_Register(TestWndProc)) {
        qDebug() << "Failed to register window procedure";
        return 1;
    }
    
    // Create a test window with a fake IP (127.0.0.1 = 0x0100007F in network byte order)
    DWORD testIP = 0x0100007F; // 127.0.0.1
    void* hWnd = CW_Create(testIP, 800, 600);
    
    if (!hWnd) {
        qDebug() << "Failed to create control window";
        return 1;
    }
    
    qDebug() << "Control window created successfully!";
    qDebug() << "Try interacting with the window - mouse clicks, keyboard input, right-click menu";
    
    int result = app.exec();
    
    WSACleanup();
    return result;
}
