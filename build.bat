@echo off
REM Build script for HVNC Server with Qt6 ControlWindow

echo Building HVNC Server with Qt6 ControlWindow...

REM Change to Server directory
cd Server

REM Create build directory
if not exist "build_qt" mkdir build_qt
cd build_qt

echo Configuring with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if errorlevel 1 (
    echo CMake configuration failed. Make sure Qt6 is installed and CMAKE_PREFIX_PATH is set.
    echo Example: set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\msvc2022_64
    cd ..\..
    pause
    exit /b 1
)

echo Building with CMake...
cmake --build . --config Release
if errorlevel 1 (
    echo Build failed.
    cd ..\..
    pause
    exit /b 1
)

echo Build succeeded!
echo.

REM Check if Server.exe was built
if exist "Release\Server.exe" (
    echo Starting HVNC Server...
    echo.
    Release\Server.exe
) else (
    echo Server.exe not found in Release directory.
    echo Build may have failed or executable is in a different location.
    dir Release\
)

cd ..\..
