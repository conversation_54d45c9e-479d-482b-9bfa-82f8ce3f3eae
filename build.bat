@echo off
REM Build script for HVNC Server with Qt6 ControlWindow

echo Building HVNC Server with Qt6 ControlWindow...

REM Try to find Qt6 installation
set "QT6_PATH="

REM Check common Qt6 installation paths
if exist "C:\Qt\6.5.3\msvc2019_64" set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64"
if exist "C:\Qt\6.5.3\msvc2022_64" set "QT6_PATH=C:\Qt\6.5.3\msvc2022_64"
if exist "C:\Qt\6.5.0\msvc2019_64" set "QT6_PATH=C:\Qt\6.5.0\msvc2019_64"
if exist "C:\Qt\6.5.0\msvc2022_64" set "QT6_PATH=C:\Qt\6.5.0\msvc2022_64"
if exist "C:\Qt\6.4.0\msvc2019_64" set "QT6_PATH=C:\Qt\6.4.0\msvc2019_64"
if exist "C:\Qt\6.4.0\msvc2022_64" set "QT6_PATH=C:\Qt\6.4.0\msvc2022_64"

if "%QT6_PATH%"=="" (
    echo Qt6 not found in common locations.
    echo Please set CMAKE_PREFIX_PATH manually, for example:
    echo set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\msvc2019_64
    cd ..\..
    pause
    exit /b 1
)

echo Found Qt6 at: %QT6_PATH%
set CMAKE_PREFIX_PATH=%QT6_PATH%

REM Change to Server directory
cd Server

REM Create build directory
if not exist "build_qt" mkdir build_qt
cd build_qt

echo Configuring with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_PREFIX_PATH="%QT6_PATH%"
if errorlevel 1 (
    echo CMake configuration failed.
    echo Make sure Qt6 is properly installed at: %QT6_PATH%
    cd ..\..
    pause
    exit /b 1
)

echo Building with CMake...
cmake --build . --config Release
if errorlevel 1 (
    echo Build failed.
    cd ..\..
    pause
    exit /b 1
)

echo Build succeeded!
echo.

REM Check if Server.exe was built
if exist "Release\Server.exe" (
    echo Starting HVNC Server...
    echo.
    Release\Server.exe
) else (
    echo Server.exe not found in Release directory.
    echo Build may have failed or executable is in a different location.
    dir Release\
)

cd ..\..
