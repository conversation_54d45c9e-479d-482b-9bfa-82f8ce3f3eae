#pragma once

#include <QtWidgets/QMainWindow>
#include <QtWidgets/QApplication>
#include <QtCore/QTimer>
#include <QtCore/QString>
#include <memory>

// Include Windows headers for HWND definition
#ifdef _WIN32
#include <windows.h>
#endif

// Forward declarations
class ControlWindow;

// Type definitions to maintain compatibility with existing code
// Note: HWND is already defined by Windows headers
typedef int BOOL;
typedef unsigned long DWORD;
typedef long long (*WNDPROC)(HWND, unsigned int, unsigned long long, long long);

// Global Qt application instance
extern std::unique_ptr<QApplication> g_qtApp;

// Function declarations with same signature as original
BOOL CW_Register(WNDPROC lpfnWndProc);
HWND CW_Create(DWORD uhid, DWORD width, DWORD height);

// Qt6 ControlWindow class
class ControlWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit ControlWindow(DWORD uhid, DWORD width, DWORD height, QWidget *parent = nullptr);
    ~ControlWindow();

    // Get the HWND equivalent (returns this pointer cast to HWND)
    HWND getHWND() const { return reinterpret_cast<HWND>(const_cast<ControlWindow*>(this)); }

    // Call the registered window procedure
    long long callWndProc(unsigned int msg, unsigned long long wParam, long long lParam);

protected:
    // Qt event handlers
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void keyReleaseEvent(QKeyEvent *event) override;
    void closeEvent(QCloseEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    void onMenuAction();

private:
    DWORD m_uhid;
    QString m_title;
    QMenu *m_systemMenu;

    void setupUI();
    void setWindowTitle();
    void createSystemMenu();
    unsigned int qtToWindowsMessage(QEvent::Type type, QMouseEvent *mouseEvent = nullptr);
    unsigned long long qtToWindowsMouseButton(Qt::MouseButton button);
    unsigned long long qtToWindowsKeyCode(int qtKey);
};